cmake_minimum_required(VERSION 3.28)
set(CMAKE_CXX_STANDARD 20)

project(PlcClient VERSION 3.0.7)

# Debug 模式下增加后缀
set(CMAKE_DEBUG_POSTFIX d)

# 版本定义
add_definitions(-D${PROJECT_NAME}_VERSION="${PROJECT_VERSION}")

# Windows下生成库(.lib)文件并包含全部符号
# set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)

# 生成文件的目录
set(CMAKE_PDB_OUTPUT_DIRECTORY pdb)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY lib)

find_package(libmodbus CONFIG REQUIRED)

# 文件列表
file (GLOB SOURCES
        "include/*.h"
        "src/*.cpp"
        "src/*.hpp"
)

list (APPEND INCLUDE_DIRECTORIES
        "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
        "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>"
)

# 生成的文件名
set(OUTPUT_FILE_NAME ${PROJECT_NAME}V3)

# 动态链接库
set(TARGET ${PROJECT_NAME})
add_library(${TARGET} SHARED)
set_target_properties(${TARGET} PROPERTIES OUTPUT_NAME ${OUTPUT_FILE_NAME})
target_include_directories(${TARGET} PUBLIC
        ${INCLUDE_DIRECTORIES}
        $<INSTALL_INTERFACE:include>)
target_sources(${TARGET} PRIVATE ${SOURCES})
target_link_libraries(${TARGET} PRIVATE modbus)

if (MSVC)
    target_compile_definitions(${TARGET} PRIVATE PLCCLIENT_API_EXPORT)
    target_compile_options(${TARGET} PRIVATE "/utf-8")
    set_target_properties(${TARGET} PROPERTIES LINK_FLAGS "/NOEXP")

    # 设置文件详细信息
    set(TARGET_COMPANY_NAME LiXiaoxin)
    set(TARGET_FILE_DESCRIPTION "PLC Client Module for OCT")
    set(TARGET_COPYRIGHT "Copyright (C) 2024 developed by LiXiaoxin, all rights reserved.")
    get_target_property(TARGET_ORIGINAL_FILENAME ${TARGET} OUTPUT_NAME)

    configure_file("${PROJECT_SOURCE_DIR}/resources/resource.rc.in" "${PROJECT_BINARY_DIR}/resource.rc")
    target_sources(${TARGET} PRIVATE "${PROJECT_BINARY_DIR}/resource.rc")
endif ()

# 静态链接库
set(TARGET_STATIC ${PROJECT_NAME}-static)
add_library(${TARGET_STATIC} STATIC)
if (CMAKE_SYSTEM_NAME STREQUAL "Windows")
    set_target_properties(${TARGET_STATIC} PROPERTIES OUTPUT_NAME "lib${OUTPUT_FILE_NAME}")

    if (CMAKE_BUILD_TYPE STREQUAL "Debug")
        set_target_properties(${TARGET_STATIC} PROPERTIES
                COMPILE_PDB_NAME lib${OUTPUT_FILE_NAME}${CMAKE_DEBUG_POSTFIX}
                COMPILE_PDB_OUTPUT_DIRECTORY ${CMAKE_PDB_OUTPUT_DIRECTORY}
        )
    elseif (CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
        set_target_properties(${TARGET_STATIC} PROPERTIES
                COMPILE_PDB_NAME lib${OUTPUT_FILE_NAME}
                COMPILE_PDB_OUTPUT_DIRECTORY ${CMAKE_PDB_OUTPUT_DIRECTORY}
        )
    endif()
else ()
    set_target_properties(${TARGET_STATIC} PROPERTIES OUTPUT_NAME ${OUTPUT_FILE_NAME})
endif ()
target_include_directories(${TARGET_STATIC} PUBLIC
        ${INCLUDE_DIRECTORIES}
        $<INSTALL_INTERFACE:include>)
target_sources(${TARGET_STATIC} PRIVATE ${SOURCES})
target_link_libraries(${TARGET_STATIC} PRIVATE modbus)

if (MSVC)
    target_compile_options(${TARGET_STATIC} PRIVATE "/utf-8")
endif ()

# 安装目录
set(CMAKE_INSTALL_PREFIX "${PROJECT_SOURCE_DIR}/install/${PROJECT_VERSION}")

set(INSTALL_INCLUDE_DIR "include")
set(INSTALL_LIB_DIR "lib")
set(INSTALL_CMAKE_DIR "cmake")

# 安装 include 目录
install(DIRECTORY include/ DESTINATION "${INSTALL_INCLUDE_DIR}")

# Windows 下拷贝 .pdb 文件
if (CMAKE_SYSTEM_NAME STREQUAL "Windows")
    get_target_property(TARGET_OUTPUT_NAME ${TARGET} OUTPUT_NAME)
    get_target_property(TARGET_STATIC_OUTPUT_NAME ${TARGET_STATIC} OUTPUT_NAME)

    if (CMAKE_BUILD_TYPE STREQUAL "Debug")
        install(FILES
                "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_PDB_OUTPUT_DIRECTORY}/${TARGET_OUTPUT_NAME}${CMAKE_DEBUG_POSTFIX}.pdb"
                "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_PDB_OUTPUT_DIRECTORY}/${TARGET_STATIC_OUTPUT_NAME}${CMAKE_DEBUG_POSTFIX}.pdb"
                DESTINATION "${INSTALL_LIB_DIR}"
        )
    elseif (CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
        install(FILES
                "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_PDB_OUTPUT_DIRECTORY}/${TARGET_OUTPUT_NAME}.pdb"
                "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_PDB_OUTPUT_DIRECTORY}/${TARGET_STATIC_OUTPUT_NAME}.pdb"
                DESTINATION "${INSTALL_LIB_DIR}"
        )
    endif ()
endif ()

# 安装编译后的库文件，同时将信息导出
install(TARGETS ${TARGET} ${TARGET_STATIC}
        EXPORT ${PROJECT_NAME}Targets
        LIBRARY DESTINATION "${INSTALL_LIB_DIR}"
        ARCHIVE DESTINATION "${INSTALL_LIB_DIR}"
        RUNTIME DESTINATION "${INSTALL_LIB_DIR}"
)

# 将导出的信息输出为文件 xxxTargets.cmake, xxxTargets-debug.cmake/xxxTargets-release.cmake
install(EXPORT ${PROJECT_NAME}Targets
        FILE ${PROJECT_NAME}Targets.cmake
        DESTINATION "${INSTALL_CMAKE_DIR}"
        NAMESPACE PlcClient::
)

# ------ 生成并安装 xxxConfig.cmake 和 xxxConfigVersion.cmake 文件 ------
include(CMakePackageConfigHelpers)

# 生成 xxxConfig.cmake 文件
configure_package_config_file(
        "cmake/PlcClientConfig.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}Config.cmake"
        INSTALL_DESTINATION "${INSTALL_CMAKE_DIR}"
        PATH_VARS INSTALL_INCLUDE_DIR INSTALL_LIB_DIR
)

# 生成 xxxConfigVersion.cmake 文件
write_basic_package_version_file(
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}ConfigVersion.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY SameMajorVersion
)

# 安装
install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}Config.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}ConfigVersion.cmake"
        DESTINATION "${INSTALL_CMAKE_DIR}"
)
# --------------------------------------------------------------------

add_subdirectory(tool/QtSimulator)
add_subdirectory(tool/Simulator)

# 安装 CppAdapter
install(DIRECTORY tool/CppAdapter/
        DESTINATION adapter/cpp
        USE_SOURCE_PERMISSIONS
)
install(FILES src/magic_enum.hpp
        DESTINATION adapter/cpp)

# 安装 QtAdapter
install(DIRECTORY tool/QtAdapter/
        DESTINATION adapter/qt
        USE_SOURCE_PERMISSIONS
)
