#include <iostream>
#include <mutex>
#include <random>

#include <Windows.h>

#include "PlcClientCppAdapter.h"
#include "magic_enum.hpp"

/**
 * 获取精确到毫秒的当前时间字符串
 * @return 结果
 */
std::string now() {
    const auto now = std::chrono::system_clock::now();
    const auto now_milliseconds = std::chrono::floor<std::chrono::milliseconds>(now);
    auto now_zoned = std::chrono::zoned_time{std::chrono::current_zone(), now_milliseconds};
    return std::format("{:%Y-%m-%d %H:%M:%S}", now_zoned);
}

void printLog(const std::string& content) {
    static std::mutex cOutMutex;

    std::lock_guard lock(cOutMutex);
    std::cout << "[" + now() + "]" + " [main.cpp] " + content << std::endl;
}

std::filesystem::path getApplicationDirPath() {
    // 尝试使用固定缓冲区（适用于大多数路径长度 < MAX_PATH 的情况）
    wchar_t buffer[MAX_PATH]{};

    // 获取当前可执行文件的完整路径
    DWORD length = GetModuleFileNameW(
        nullptr,        // 获取当前进程的可执行文件路径
        buffer,         // 输出缓冲区
        MAX_PATH        // 缓冲区大小
    );

    // 处理路径超长的情况
    if (length >= MAX_PATH && GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
        // 获取实际需要的缓冲区大小（包含 null 终止符）
        length = GetModuleFileNameW(nullptr, nullptr, 0);
        if (length == 0) {
            throw std::runtime_error("Failed to get executable path length");
        }

        // 动态分配足够大的缓冲区
        std::wstring longBuffer(length, L'\0');
        const DWORD newLength = GetModuleFileNameW(
            nullptr,
            longBuffer.data(),
            length
        );

        if (newLength == 0 || newLength >= length) {
            throw std::runtime_error("Failed to get long executable path");
        }

        // 转换为 filesystem::path 并获取父目录
        return std::filesystem::path(longBuffer).parent_path();
    }
    // 处理常规错误
    if (length == 0) {
        throw std::runtime_error("Failed to get executable path");
    }

    // 常规情况：直接使用栈上的缓冲区
    return std::filesystem::path(buffer).parent_path();
}

int main() {
    SetConsoleOutputCP(CP_UTF8);
    printLog(std::format("PlcClient Version: {}", PlcClientCppAdapter::getInstance().getVersion()));

    PlcClientCppAdapter::getInstance().log = [](const PlcClientCppAdapter::LogType &logType, const std::string &log) {
        if (logType != PlcClientCppAdapter::LogType::DEBUG) {
            printLog(log);
        }
    };

    PlcClientCppAdapter::getInstance().modbusConnectionStateChanged = [](const std::size_t modbusChannelIndex, const bool connected) {
        printLog(std::format("第{}个Modbus连接的状态改变:{}", modbusChannelIndex, connected));
    };

    PlcClientCppAdapter::getInstance().runningStateChanged = [](const PlcClientCppAdapter::RunningState &runningState) {
        printLog(std::format("运行状态改变:{}", magic_enum::enum_name(runningState)));
    };

    PlcClientCppAdapter::getInstance().stateWordChanged = [](const std::uint16_t stateWord) {
        printLog(std::format("状态字改变:{}", stateWord));
    };

    PlcClientCppAdapter::getInstance().alarmStateChanged = [](const std::array<bool, 160> &alarms) {
        std::ostringstream oss;
        for (std::size_t i=0;i<alarms.size();i++) {
            if (i%16 == 0) {
                oss << "  " << std::endl << std::format("[{:3}-{:3}] ",i, i+16);
            }
            oss << std::to_string(alarms[i]);
        }
        printLog(std::format("报警状态改变：{}", oss.str()));
    };

    PlcClientCppAdapter::getInstance().workstationStarted2D = [](const std::size_t workstation2DIndex, const std::string &trayCode) {
        printLog(std::format("2D工位{} 工位开始 托盘号:{}", workstation2DIndex+1, trayCode));
    };

    PlcClientCppAdapter::getInstance().positionGroupStarted2D = [](const std::size_t workstation2DIndex, const std::size_t horizontalPositionIndex, const std::size_t positionGroupIndex) {
        printLog(std::format("2D工位{} 水平点位{} 点位组{} 开始", workstation2DIndex+1, horizontalPositionIndex, positionGroupIndex));
    };

    PlcClientCppAdapter::getInstance().queryPositionGroupResult2D = [](const std::size_t workstation2DIndex, const std::size_t horizontalPositionIndex, const std::size_t positionGroupIndex, bool &result) {
        result = true;
        printLog(std::format("2D工位{} 水平点位{} 点位组{} 查询结果:{}", workstation2DIndex+1, horizontalPositionIndex, positionGroupIndex, result));
    };

    PlcClientCppAdapter::getInstance().workstationFinished2D = [](const std::size_t workstation2DIndex) {
        printLog(std::format("2D工位{} 工位结束", workstation2DIndex+1));
    };

    PlcClientCppAdapter::getInstance().workstationStarted3D = [](const std::size_t workstation3DIndex, const std::string &trayCode) {
        printLog(std::format("3D工位{} 工位开始 托盘号:{}", workstation3DIndex+1, trayCode));
    };

    std::ptrdiff_t currentHorizontalPositionIndex3D = 0;
    std::size_t horizontalPositionCount = 0;
    PlcClientCppAdapter::getInstance().positionStarted3D = [&currentHorizontalPositionIndex3D, &horizontalPositionCount](const std::size_t workstation3DIndex, std::ptrdiff_t &horizontalPositionIndex, std::size_t &positionIndex) {
        if (currentHorizontalPositionIndex3D < horizontalPositionCount) {
            horizontalPositionIndex = currentHorizontalPositionIndex3D++;
            positionIndex = 0;
        } else {
            currentHorizontalPositionIndex3D = 0;
            horizontalPositionIndex =-1;
            positionIndex = 0;
        }

        printLog(std::format("3D工位{} 水平点位{} 点位{} 开始", workstation3DIndex+1, horizontalPositionIndex, positionIndex));
    };

    PlcClientCppAdapter::getInstance().positionArrived3D = [](const std::size_t workstation3DIndex, const std::ptrdiff_t horizontalPositionIndex, const std::size_t positionIndex) {
        printLog(std::format("3D工位{} 水平点位{} 点位{} 已到达", workstation3DIndex+1, horizontalPositionIndex, positionIndex));
    };

    PlcClientCppAdapter::getInstance().workstationFinished3D = [](const std::size_t workstation3DIndex) {
        printLog(std::format("3D工位{} 工位结束", workstation3DIndex+1));
    };

    PlcClientCppAdapter::getInstance().queryResult = [](const std::size_t workstationUnloadIndex, const std::string &trayCode, std::array<std::uint8_t, 400> &result) {
        std::random_device rd;  // 非确定性种子
        std::mt19937 gen(rd());

        // 定义分布范围
        std::uniform_int_distribution<int> dis(1, 3);



        std::ostringstream oss;

        oss << std::endl;
        for (unsigned char & i : result) {
            i = dis(gen);
            oss << static_cast<int>(i);
        }

        printLog(std::format("下料工位{} 托盘号:{} 查询结果:{}", workstationUnloadIndex+1, trayCode, oss.str()));
    };

    PlcClientCppAdapter::getInstance().sortResult = [](const std::size_t workstationUnloadIndex, const std::string &trayCode, std::vector<PlcClientCppAdapter::SortResult> &sortResults, bool &result) {
        result = true;

        std::ostringstream oss;

        oss << std::endl;
        for (const auto & [trayType, trayIndex, holeIndex] : sortResults) {
            oss << "托盘类型:" << trayType << " 托盘编号:" << trayIndex << " 穴位编号:" << holeIndex << std::endl;
        }

        printLog(std::format("下料工位{} 托盘号:{} 分拣结果:{}", workstationUnloadIndex+1, trayCode, oss.str()));
    };

    try {
        const auto configure = PlcClientCppAdapter::Configure::fromJson(getApplicationDirPath().string() + "\\PlcClientConfigure.json");

        printLog("读取 PlcClientConfigure.json 成功");

        if (!configure.workstation3Ds.empty()) {
            horizontalPositionCount = configure.workstation3Ds.at(0).horizontalPositionSize;
        }

        if (!PlcClientCppAdapter::getInstance().start(configure)) {
            printLog("启动失败");
            return -1;
        }

        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(10));
        }

        PlcClientCppAdapter::getInstance().stop();

    } catch (const std::exception& e) {
        printLog(e.what());
        return -2;
    }

    return 0;
}
